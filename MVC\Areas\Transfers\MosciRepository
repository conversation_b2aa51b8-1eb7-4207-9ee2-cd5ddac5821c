using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Odrc.Dots.Data.Common.Utilities;
using Odrc.Dots.Data.HelperMethods;
using Odrc.Dots.Data.Transfers.Interfaces;
using Odrc.Dots.Entities.Transfers.WorkLists;
using System;
using Odrc.Dots.Entities.Rh;
using Odrc.Dots.Entities.Common.Lookup;
using Odrc.Dots.Core;
using Odrc.Dots.Entities.Common;
using System.Configuration;
using Odrc.Dots.Entities.Transfers;

namespace Odrc.Dots.Data.Transfers.RepositoryClasses
{
    public class MosciRepository : IMosciRepository
    {
        public List<MosciData> GetMosciInfoByOaksId(string oaksId)
        {
            // Initialise the list that will be returned
            var mosciDataList = new List<MosciData>();

            using (var connection = new SqlConnection(ConfigurationManager.ConnectionStrings[GlobalConstants.ConnectionStringDots].ToString()))
            using (var command = new SqlCommand("[dbo].[sp1314_CMENU_GetMOSCHRecords]", connection))
            {
                command.CommandType = CommandType.StoredProcedure;

                // Allow null or empty OID
                oaksId = string.IsNullOrWhiteSpace(oaksId) ? null : oaksId;
                command.Parameters.AddWithValue("@OID",(object)oaksId ?? DBNull.Value);
                try
                {
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        // Read all rows instead of just one
                        while (reader.Read())
                        {
                            var mosciData = new MosciData
                            {
                                Oid = reader["OID"].ToString().Trim(),
                                //SchdInst = (int?)reader["SCHDINST"],
                                //Instno = (int?)reader["INSTNO"],
                                Descrl = reader["COMM"].ToString().Trim(),
                                Rowid = reader["ROWID"].ToString().Trim(),
                            };

                            int SchdInst = reader.GetOrdinal ("SCHDINST");
                            mosciData.SchdInst = reader.IsDBNull(SchdInst) ? (int?) null : reader.GetInt32(SchdInst);
                            int Instno = reader.GetOrdinal("INSTNO");
                            mosciData.Instno = reader.IsDBNull(Instno) ? (int?)null : reader.GetInt32(Instno);
                            // Instno = (int?)reader["INSTNO"],

                            var schdate = reader["SCHDATE"];
                            if (schdate != DBNull.Value)
                            {
                                mosciData.SchDate = Convert.ToDateTime(schdate);
                            }

                            mosciDataList.Add(mosciData);
                        }
                    }
                }
                catch (Exception ex)
                {
                     throw new Exception( ex.Message);
                }
            }

            return mosciDataList;
        }

        
        public int InsertorUpdateMosci(string combineOffenderId, string SchDate, int Instno, int SchdInst, string Descrl, string username, string rowId)
        {
            int result = 0;
            using (var connection = new SqlConnection(Helper.ConnectionString))
                try
                {
                    using (var cmd = new SqlCommand("[dbo].[sp1314_CMENU_Save_Move]", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;


                        cmd.Parameters.Add("@OID", SqlDbType.VarChar, 10).Value = combineOffenderId;
                        //cmd.Parameters.Add("@SCHDATE", SqlDbType.VarChar, 10).Value = SchDate;
                        var pSchDate = cmd.Parameters.Add("@SCHDATE", SqlDbType.VarChar, 10);
                        if (string.IsNullOrEmpty(SchDate))
                        {
                            pSchDate.Value = DBNull.Value;
                        }
                        else if (DateTime.TryParse(SchDate, out var parsedDate))
                        {
                            pSchDate.Value = parsedDate.ToString("yyyy-MM-dd");
                        }
                        else
                        {
                            throw new ArgumentException("$SchDate  is not in recognize format ");
                        }
                        cmd.Parameters.Add("@INSTNO", SqlDbType.Int).Value = Instno;
                        cmd.Parameters.Add("@SCHDINST", SqlDbType.Int).Value = SchdInst;
                        cmd.Parameters.Add("@COMM", SqlDbType.VarChar, 50).Value = Descrl;
                        cmd.Parameters.Add("@USERID", SqlDbType.VarChar, 50).Value = username;
                        cmd.Parameters.Add("@ROWID", SqlDbType.VarChar, 50).Value = string.IsNullOrEmpty(rowId) ? (object)DBNull.Value : rowId;

                        cmd.Connection = connection;
                        connection.Open();
                        result = cmd.ExecuteNonQuery();
                        connection.Close();


                    }
                }
                catch (Exception ex)
                {
                    throw new Exception(ex.Message);
                }
            return result;
        }

        public int DeleteMosciRecord(string rowId, string username)
        {
            int result = 0;
            using (var connection = new SqlConnection(Helper.ConnectionString))
            using (var cmd = new SqlCommand("[dbo].[sp1314_CMENU_DeleteMoveRecord]", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@USERID", SqlDbType.VarChar, 50).Value = username;
                cmd.Parameters.Add("@ROWID", SqlDbType.VarChar, 50).Value = rowId;
                

                cmd.Connection = connection;
                connection.Open();
                result = cmd.ExecuteNonQuery();
                connection.Close();
            }

            return result;
        }

        // check the offender exist before delete in trans 3 screen
        public bool CheckIfOffenderExists(string OID, string schdate)
        {
            bool strResult = false;

            using (var connection = new SqlConnection(Helper.ConnectionString))
            using (var cmd = new SqlCommand("[dbo].[sp1314_TRANSPORT_CheckOffenderExistsWithoutTripid]", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@DATE", SqlDbType.VarChar, 10).Value = schdate;
                cmd.Parameters.Add("@OID", SqlDbType.VarChar, 10).Value = OID;

                var adapter = new SqlDataAdapter(cmd);
                var ds = new DataSet();
                adapter.Fill(ds);

                foreach (DataRow row in ds.Tables[0].Rows)
                {
                    if (row["EXIST"].ToString() != "0")
                    {
                        strResult = true;
                        break;
                    }
                }
            }

            return strResult;
        }

    }
}